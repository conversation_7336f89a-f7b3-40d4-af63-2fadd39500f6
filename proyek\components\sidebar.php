<?php
// Enhanced Sidebar component untuk Antosa Arsitek Project Management
// Pastikan session sudah dimulai sebelum include file ini

// Function to get task count for badges
function getTaskCount($type = 'pending') {
    global $koneksi;
    if (!isset($koneksi)) {
        require_once '../koneksi.php';
    }

    try {
        switch($type) {
            case 'pending':
                $query = "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'proses'";
                break;
            case 'verification':
                $query = "SELECT COUNT(*) as count FROM verifikasi WHERE status_verifikasi = 'pending'";
                break;
            default:
                return 0;
        }

        $result = mysqli_query($koneksi, $query);
        if ($result) {
            $data = mysqli_fetch_assoc($result);
            return $data['count'];
        }
    } catch (Exception $e) {
        // Silently handle errors
    }
    return 0;
}

$current_page = basename($_SERVER['PHP_SELF']);
$pending_tasks = getTaskCount('pending');
$pending_verifications = getTaskCount('verification');
?>

<!-- Sidebar -->
<ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

    <!-- Sidebar - Brand -->
    <a class="sidebar-brand d-flex align-items-center justify-content-center" href="proyek.php">
        <div class="sidebar-brand-icon rotate-n-15">
            <i class="fas fa-drafting-compass"></i>
        </div>
        <div class="sidebar-brand-text mx-3">Antosa Arsitek</div>
    </a>

    <!-- Divider -->
    <hr class="sidebar-divider my-0">

    <!-- Nav Item - Dashboard -->
    <li class="nav-item <?php echo ($current_page == 'proyek.php') ? 'active' : ''; ?>">
        <a class="nav-link" href="proyek.php">
            <i class="fas fa-fw fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </li>

    <!-- Divider -->
    <hr class="sidebar-divider">

    <!-- Heading -->
    <div class="sidebar-heading">
        Manajemen Proyek
    </div>

    <!-- Nav Item - Tugas Harian -->
    <li class="nav-item <?php echo ($current_page == 'tugas_harian.php') ? 'active' : ''; ?>">
        <a class="nav-link" href="tugas_harian.php">
            <i class="fas fa-fw fa-tasks"></i>
            <span>Tugas Harian</span>
            <?php if ($pending_tasks > 0): ?>
                <span class="badge badge-warning badge-counter ml-auto"><?php echo $pending_tasks; ?></span>
            <?php endif; ?>
        </a>
    </li>

    <!-- Nav Item - Input Tugas -->
    <li class="nav-item <?php echo ($current_page == 'input_tugas.php') ? 'active' : ''; ?>">
        <a class="nav-link" href="input_tugas.php">
            <i class="fas fa-fw fa-plus-circle"></i>
            <span>Input Tugas Baru</span>
        </a>
    </li>

    <!-- Divider -->
    <hr class="sidebar-divider">

    <!-- Heading -->
    <div class="sidebar-heading">
        File & Verifikasi
    </div>

    <!-- Nav Item - Upload File -->
    <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'uploud_file.php') ? 'active' : ''; ?>">
        <a class="nav-link" href="uploud_file.php">
            <i class="fas fa-fw fa-cloud-upload-alt"></i>
            <span>Upload File Desain</span>
        </a>
    </li>

    <!-- Nav Item - Verifikasi -->
    <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'verifikasi.php') ? 'active' : ''; ?>">
        <a class="nav-link" href="verifikasi.php">
            <i class="fas fa-fw fa-check-double"></i>
            <span>Verifikasi</span>
        </a>
    </li>

    <!-- Divider -->
    <hr class="sidebar-divider d-none d-md-block">

    <!-- Nav Item - Logout -->
    <li class="nav-item">
        <a class="nav-link" href="../logout.php" onclick="return confirm('Apakah Anda yakin ingin keluar?')">
            <i class="fas fa-sign-out-alt"></i>
            <span>Keluar</span>
        </a>
    </li>

    <!-- Sidebar Toggler (Sidebar) -->
    <div class="text-center d-none d-md-inline">
        <button class="rounded-circle border-0" id="sidebarToggle"></button>
    </div>

</ul>
<!-- End of Sidebar -->
